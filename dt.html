<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="css/style.min.css">
    <link rel="stylesheet" href="css/animate.css">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> 
    <script> var vConsole = new window.VConsole(); </script> -->
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div class="index" id="app" v-cloak style="height:auto;min-height:110vh">

        <img src="img/logo2023.png?v=1" class="logo animate__animated animate__backInLeft" style="width: 13.4vw;">
        <img src="img/icon.png" class="icon animate__animated animate__backInRight">
        <div class="tit2">皖嫂家政技能认证<br>{$detail.subtitle}</div>
        <div class="dt-container">
            <img src="img/dt1.png" class="dt1">
            <div class="dt-area">
                <div class="dt-data">
                    <p>第{{serialNumber}}/{{list.length}}题</p>
                </div>
                <div class="dt-content">
                    <div class="question">【{{qesType(now.type)}}】{{now.tit}}</div>
                    <ul class="answer">
                        <li v-for="(item,index) in now.qes" :class="myStyle(index)" @click="pick(index)">
                            <div class="left" v-if="index==0">A</div>
                            <div class="left" v-if="index==1">B</div>
                            <div class="left" v-if="index==2">C</div>
                            <div class="left" v-if="index==3">D</div>
                            <div class="left" v-if="index==4">E</div>
                            <div class="right">{{item}}</div>
                        </li>
                    </ul>
                </div>
                <div v-if="showAsw&&!right" class="answer-asw">回答错误，本题答案为：{{now.asw}}</div>
                <div v-if="showAsw&&right" class="answer-asw2">恭喜您回答正确</div>
                <div class="next" @click="prev" v-if="serialNumber>1">上一题</div>
                <div class="next" @click="submit" v-if="!showAsw">确定</div>
                <div class="next" @click="next" v-if="showAsw&&serialNumber!==list.length">下一题</div>
            </div>
        </div>
        <img src="img/bg2.png" class="bg1 animate__animated animate__fadeIn">
        <!-- 分数弹窗 -->
        <div class="mask animate__animated animate__fadeIn" style="animation-duration: 0.5s;" v-if="show===1">
            <div class="popup score">
                <img src="img/close.png" class="close" @click="goIndex">
                <div class="score-content">
                    <p>本次训练中</p>
                    <p>答对<span>{{correctNum}}</span>题/已回答<span>{{list.length}}</span>题</p>
                </div>
                <div class="back" @click="goIndex">返回首页</div>
            </div>
        </div>
    </div>
    <script src="js/libs/jquery3.4.1.js"></script>
    <script src="js/libs/vue.js"></script>
    <script>
        const conversion = str => String(str).split('').map(char => 'ABCDEF'[char]).join('');
        const conversionReverse = str => str.split('').map(char => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.indexOf(char)).join('');
        function throttle(fn, wait = 1000) {
            var pre = Date.now();
            var firstFlag = true;
            return function() {
                var context = this;
                var args = arguments;
                var now = Date.now();
                if ((now - pre >= wait) || firstFlag) {
                    firstFlag = false;
                    pre = Date.now();
                    return fn.apply(context, args);
                }
            }
        }
        var app = new Vue({
            el: "#app",
            data() {
                return {
                    now:{},
                    show:0,
                    flag:true,
                    pickIndex:[],
                    list:  {$questions|raw},
                    showAsw:false,
                    right:false,
                    correctNum:0,
                    serialNumber:{$nextQuestion},//当前第几题
                    recordList:{$records|raw},//上次答题的答案数据
                }
            },
            mounted() {
                // 设置问题
                this.now = this.list[this.serialNumber-1]
            },
            computed:{
                qesType(){
                    return type => {
                        if (type === 1) return '单选题'
                        if (type === 2) return '多选题'
                        if (type === 3) return '判断题'
                        if (type === 4) return '填空题'
                    }
                },
                myStyle() {
                    return index => {
                        const pick = this.pickIndex.includes(conversion(index)); // 是否选中
                        if (!this.showAsw) return pick ? 'pick' : '';
                        const cur = [...this.now.asw].includes(conversion(index)); // 是否是正确
                        let ac = true; // 是否只选择了错误答案
                        for (const iterator of this.pickIndex) {
                            if (this.now.asw.includes(iterator)) {
                                ac = false;
                            }
                        }
                        if (pick && cur) return 'cur cur-icon';
                        if (!this.right && pick && !cur) return 'err err-icon';
                        if (!this.right && !pick && cur && ac) return 'cur cur-icon';
                        if (!this.right && !pick && cur && !ac) return 'cur';
                        return '';
                    };
                }
            },
            methods: {
                pick(index){
                    if(this.showAsw)return
                    // 单选
                    if (this.now.type === 1 || this.now.type === 3 || this.now.type === undefined) {
                        this.pickIndex = [conversion(index)]
                    }
                    // 多选
                    if (this.now.type === 2) {
                        let exist = this.pickIndex.indexOf(conversion(index))
                        if (exist === -1) {
                            this.pickIndex = this.pickIndex.concat([conversion(index)])
                        } else {
                            this.pickIndex.splice(exist, 1)
                        }
                    }
                },
                // 返回首页
                goIndex(){
                    window.location.href = 'index';
                },
                // 下一题
                next(){
                    if(this.serialNumber===this.list.length){
                        alert('答题结束')
                        return this.goIndex()
                    }
                    this.serialNumber++
                    this.now = this.list[this.serialNumber-1]
                    if(this.recordList[this.serialNumber-1]){
                        this.setStatus()
                    }else{
                        this.pickIndex= []
                        this.showAsw = false
                        this.right = false
                    }
                },
                prev(){
                    this.serialNumber--
                    this.now = this.list[this.serialNumber-1]
                    this.setStatus()
                },
                setStatus(){
                    this.pickIndex = this.recordList[this.serialNumber-1].split('')
                    this.judge()
                },
                // 判断正误
                judge(){
                    if (this.pickIndex[0] === undefined) return alert('请选择答案')
                    this.showAsw = true
                    if (this.pickIndex.sort().join('') == this.now.asw) {
                        this.right = true
                    } else {
                        this.right = false
                    }
                },
                //提交答题结果
                submit:throttle(function(){
                    if (this.pickIndex[0] === undefined)return alert('请选择答案')
                    this.judge()
                    this.recordList.push(this.pickIndex.sort().join(''))
                    $.ajax({
                        type: "post",
                        url: 'datisub',
                        dataType: 'json',
                        data: {
                            serialNumber:this.serialNumber,
                            pick:this.pickIndex.sort().join(''),
                            id:this.now.id,
                            right:this.right,
                            trainType: {$detail.trainType},
                            turns: {$detail.turns},
                            sign: "{$detail.sign}",
                        },
                        success: res => {
                            //答题结束回调
                            if(res.status===2){
                                this.correctNum = res.correctNum
                                this.show=1
                            }
                            if(res.status === 0){
                                alert(res.msg);
                                location.reload();
                            }
                            if(res.status===-1){
                                alert(res.msg);
                                window.location.href = 'index';
                            }
                        },
                        error: res => {
                            alert('网络错误，请稍后重试')
                            window.location.reload()
                        }
                    })
                }),
            }
        })
    </script>
    {include file="share"/}
</body>

</html>