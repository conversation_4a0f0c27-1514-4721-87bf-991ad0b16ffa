<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios"
    name="viewport">
  <title>{$zt_name}</title>
  <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css?v=1">
  <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css" />
  <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
  <link rel="stylesheet" href="css/index.css?<?php echo mt_rand(1000,999999);?>">
  <style>
    html {
      font-size: 18px !important;
    }
    .app-container {
      width: 100%;
      height: 100vh;
      background: #f6f7fb;
    }

    [v-cloak] {
      display: none !important;
    }
  </style>
</head>

<body class="app-container">
  <div id="app" class="w-full h-full flex flex-col bg-white" v-cloak>
    <!-- 弹窗 -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
        <div class="text-center">
          <div class="mb-4">
            <i class="fas fa-user-edit text-blue-500 text-4xl"></i>
          </div>
          <h3 class="text-lg font-semibold text-gray-800 mb-3">请录入信息</h3>
          <div class="space-y-3 mb-6">
            <div class="text-left">
              <label for="modalUserName" class="block text-xs text-slate-600 mb-1">姓名</label>
              <input v-model="userName" id="modalUserName" type="text" placeholder="请输入姓名"
                class="w-full px-3 py-2 rounded-xl border border-slate-200 text-sm outline-none focus:ring-2 focus:ring-[#e72726]/20 focus:border-[#e72726]" />
            </div>
            <div class="text-left">
              <label for="modalUserId" class="block text-xs text-slate-600 mb-1">身份证号</label>
              <input v-model="userId" id="modalUserId" type="text" placeholder="请输入18位身份证号"
                class="w-full px-3 py-2 rounded-xl border border-slate-200 text-sm outline-none focus:ring-2 focus:ring-[#e72726]/20 focus:border-[#e72726]" />
            </div>
          </div>
          <button @click="handleConfirm"
            class="w-full py-3 px-4 bg-[#e72726] text-white rounded-xl font-medium hover:bg-red-600 transition-colors">
            确认并进入
          </button>
        </div>
      </div>
    </div>

    <!-- 主应用 -->
    <div v-if="!showModal" class="w-full h-full flex flex-col bg-white">
      <!-- 首页 -->
      <template v-if="currentPage === 'home'">
        <div class="warp">
          <div class="button-container animate__animated animate__backInRight">
              <p @click="show=2">训练情况</p>
              <p @click="navigateTo('mistakes')">我的错题</p>
          </div>
          <img src="img/title.png?v=1" alt="" class="title">
          <div class="text-center text-xs text-white mt-6">主办单位：庐阳区总工会</div>
          <!-- <div class="new_title">{{rtData.trainTitle}}</div> -->
          <!-- <img src="img/button1.png?v=1" alt="" class="button1 mt-10 pulsate-bck2" @click="loadQuestions"> -->
          <div class="start animate__animated animate__zoomInDown" @click="loadQuestions" v-if="rtData.progressCode===0">开始训练</div>
          <div class="start animate__animated animate__zoomInDown" @click="loadQuestions" v-if="rtData.progressCode===1">继续训练</div>
          <div class="start animate__animated animate__rollIn" v-if="[1,2].includes(rtData.progressCode)&&rtData.allowAgain===1" @click="again">重新开始</div>
        </div>
      </template>

      <!-- 答题页 -->
      <template v-if="currentPage === 'quiz'">
        <main class="warp warp3">
            <div class="tit2">{{rtData.trainTitle}}</div>
            <div class="dt-container">
              <img src="img/dt1.png" class="dt1">
              <div class="dt-area">
                  <div class="dt-data">
                      <p>第{{currentQuestionIndex + 1}}/{{questions.length}}题</p>
                  </div>
                  <div class="dt-content">
                      <div class="question">【{{ questionTypeLabel(currentQuestion.type) }}】{{currentQuestion.title}}</div>
                      <div v-if="currentQuestion.type === 'blank'" class="mt-3 space-y-2">
                        <input v-model="userAnswers[currentQuestion.id][0]"
                          class="w-full rounded-xl border border-slate-200 px-3 py-2 text-sm" placeholder="请输入答案" />
                      </div>
                      <div v-if="['single', 'multiple', 'judge'].includes(currentQuestion.type)" class="option-area">
                        <div v-for="(opt, i) in currentQuestion.options" :key="i" @click="selectOption(currentQuestion, i)"
                          class="block rounded-xl border p-3 mt-3 text-sm flex items-start gap-3 cursor-pointer transition-colors duration-200"
                          :class="getOptionClass(currentQuestion, i)">
                          <div
                            class="w-5 h-5 flex-shrink-0 mt-0.5 flex items-center justify-center border rounded-full transition-all duration-200"
                            :class="{
                              'border-red-500 bg-red-500': isOptionSelected(currentQuestion, i) && !questionResults[currentQuestion.id],
                              'border-slate-300': !isOptionSelected(currentQuestion, i) && !questionResults[currentQuestion.id],
                              'rounded-md': currentQuestion.type === 'multiple',
                              'border-green-500 bg-green-500': questionResults[currentQuestion.id] && currentQuestion.answer.includes(optionKey(i)),
                              'border-rose-500 bg-rose-500': questionResults[currentQuestion.id] && !currentQuestion.answer.includes(optionKey(i)) && isOptionSelected(currentQuestion, i),
                              'border-slate-300': questionResults[currentQuestion.id] && !currentQuestion.answer.includes(optionKey(i)) && !isOptionSelected(currentQuestion, i)
                            }">
                            <i v-if="isOptionSelected(currentQuestion, i) || (questionResults[currentQuestion.id] && currentQuestion.answer.includes(optionKey(i)))"
                              class="fas text-white text-[10px]"
                              :class="{'fa-check': currentQuestion.type !== 'judge', 'fa-check-circle': currentQuestion.type === 'judge' && currentQuestion.answer.includes(optionKey(i)), 'fa-times-circle': questionResults[currentQuestion.id] && !currentQuestion.answer.includes(optionKey(i)) && isOptionSelected(currentQuestion, i)}"></i>
                          </div>
                          <span><b>{{ optionKey(i) }}.</b> {{ opt }}</span>
                        </div>
                      </div>
                  </div>
                  <div v-if="questionResults[currentQuestion.id]==='wrong'" class="answer-asw">回答错误，本题答案为：{{ currentQuestion.answer.join('')}}</div>
                  <div v-if="questionResults[currentQuestion.id]==='right'" class="answer-asw2">恭喜您回答正确</div>
                  <div class="button_area">
                    <div class="next" @click="prevQuestion" v-if="currentQuestionIndex>0">上一题</div>
                    <div class="next" @click="submitCurrent" v-if="!questionResults[currentQuestion.id]">确定</div>
                    <div class="next" @click="nextQuestion" v-if="questionResults[currentQuestion.id]&&currentQuestionIndex!==questions.length">下一题</div>
                  </div>
              </div>
            </div>
            <div v-if="isSheetOpen" @click="isSheetOpen = false" class="mask"></div>
            <div class="fixed left-0 right-0 bottom-0 transition-transform duration-300 bg-white rounded-t-2xl shadow-2xl"
              :style="{ transform: isSheetOpen ? 'translateY(0)' : 'translateY(100%)' }">
              <div class="p-3 flex items-center justify-between border-b border-slate-100">
                <div class="text-sm font-medium text-slate-800">题目选择器</div>
                <button @click="isSheetOpen = false" class="text-slate-500"><i class="fas fa-times"></i></button>
              </div>
              <div class="p-3 grid grid-cols-7 gap-2 pb-6 max-h-[50vh] overflow-y-auto">
                <button v-for="(q, i) in questions" :key="q.id" @click="selectQuestion(i)"
                  class="text-xs rounded-lg border py-1.5" :class="getSheetButtonClass(q, i)">{{ i + 1 }}</button>
              </div>
            </div>
        </main>
      </template>

      <!-- 错题本页 -->
      <template v-if="currentPage === 'mistakes'">
        <div class="warp warp2">
          <!-- <img src="img/bg1.png" class="bg1 animate__animated animate__fadeIn"> -->
          <div class="px-4 py-2 bg-white border-b border-slate-100 rounded-2xl">
            <div class="flex items-center gap-2 text-xs">
              <button @click="mistakeFilter = 'all'"
                :class="mistakeFilter === 'all' ? 'border-[#f6a61d] text-[#a56300] bg-[#fff7e6]' : 'border-slate-200 text-slate-700'"
                class="px-3 py-1.5 rounded-full border">全部</button>
              <button @click="mistakeFilter = 'single'"
                :class="mistakeFilter === 'single' ? 'border-[#f6a61d] text-[#a56300] bg-[#fff7e6]' : 'border-slate-200 text-slate-700'"
                class="px-3 py-1.5 rounded-full border">单选</button>
              <button @click="mistakeFilter = 'multiple'"
                :class="mistakeFilter === 'multiple' ? 'border-[#f6a61d] text-[#a56300] bg-[#fff7e6]' : 'border-slate-200 text-slate-700'"
                class="px-3 py-1.5 rounded-full border">多选</button>
              <button @click="mistakeFilter = 'judge'"
                :class="mistakeFilter === 'judge' ? 'border-[#f6a61d] text-[#a56300] bg-[#fff7e6]' : 'border-slate-200 text-slate-700'"
                class="px-3 py-1.5 rounded-full border">判断</button>
              <button @click="mistakeFilter = 'blank'"
                :class="mistakeFilter === 'blank' ? 'border-[#f6a61d] text-[#a56300] bg-[#fff7e6]' : 'border-slate-200 text-slate-700'"
                class="px-3 py-1.5 rounded-full border">填空</button>
            </div>
          </div>
          <main class="flex-1 overflow-auto relative z-10">
            <div v-if="filteredMistakes.length === 0" class="text-center text-[#fff] text-sm py-10">暂无该类型错题</div>
            <div v-else class="p-4">
              <div v-for="item in filteredMistakes" :key="item.id"
                class="mb-3 bg-white border border-slate-100 rounded-2xl p-3">
                <div class="text-xs text-rose-700"><i class="fas fa-times-circle mr-1"></i>答错 · {{
                  questionTypeLabel(item.type) }}</div>
                <div class="mt-1 text-sm font-medium text-slate-800">{{ item.title }}</div>
                <div class="mt-2 text-xs text-slate-600">你的答案：<span class="text-rose-600 font-medium">{{
                    formatAnswer(item.userAnswer) }}</span></div>
                <div class="text-xs text-slate-600">正确答案：<span class="text-green-600 font-medium">{{item.answer.join('')}}</span></div>
                <!-- <div class="mt-1 text-xs text-slate-600">解析：{{ item.analysis || '无' }}</div> -->
              </div>
            </div>
          </main>
          <div class="back" @click="navigateTo('home')">返回首页</div>  
        </div>
      </template>

      <!-- 成绩页 -->
      <!-- <template v-if="currentPage === 'result'">
        <header class="bg-white border-b border-slate-100 px-4 py-2 flex items-center justify-between">
          <div class="text-sm font-semibold text-slate-800">成绩</div>
        </header>
        <main class="flex-1 overflow-auto p-4">
          <section class="bg-gradient-to-br from-[#e72726] to-[#f6a61d] rounded-3xl text-white p-6 shadow">
            <div class="text-xs opacity-80">2025年全市社会化工会工作者业务技能竞赛</div>
            <div class="mt-2 text-5xl font-bold leading-tight">{{ resultsSummary.score }}</div>
            <div class="mt-1 text-sm">正确 {{ resultsSummary.correctCount }}/{{resultsSummary.total }}</div>
          </section>
          <section class="mt-4 grid grid-cols-2 gap-3">
            <div class="rounded-2xl border border-slate-100 p-4">
              <div class="text-slate-500 text-xs">单选正确率</div>
              <div class="text-lg font-semibold text-slate-800">{{ resultsSummary.percentages.single }}%</div>
              <div class="mt-1 h-1.5 bg-slate-100 rounded-full overflow-hidden">
                <div class="h-full bg-[#e72726]" :style="{ width: resultsSummary.percentages.single + '%' }"></div>
              </div>
            </div>
            <div class="rounded-2xl border border-slate-100 p-4">
              <div class="text-slate-500 text-xs">多选正确率</div>
              <div class="text-lg font-semibold text-slate-800">{{ resultsSummary.percentages.multiple }}%</div>
              <div class="mt-1 h-1.5 bg-slate-100 rounded-full overflow-hidden">
                <div class="h-full bg-[#f6a61d]" :style="{ width: resultsSummary.percentages.multiple + '%' }"></div>
              </div>
            </div>
            <div class="rounded-2xl border border-slate-100 p-4">
              <div class="text-slate-500 text-xs">判断正确率</div>
              <div class="text-lg font-semibold text-slate-800">{{ resultsSummary.percentages.judge }}%</div>
              <div class="mt-1 h-1.5 bg-slate-100 rounded-full overflow-hidden">
                <div class="h-full bg-[#e72726]" :style="{ width: resultsSummary.percentages.judge + '%' }"></div>
              </div>
            </div>
            <div class="rounded-2xl border border-slate-100 p-4">
              <div class="text-slate-500 text-xs">填空正确率</div>
              <div class="text-lg font-semibold text-slate-800">{{ resultsSummary.percentages.blank }}%</div>
              <div class="mt-1 h-1.5 bg-slate-100 rounded-full overflow-hidden">
                <div class="h-full bg-[#f6a61d]" :style="{ width: resultsSummary.percentages.blank + '%' }"></div>
              </div>
            </div>
          </section>
          <section class="mt-4 grid grid-cols-2 gap-3 pb-24">
            <button @click="navigateTo('mistakes')"
              class="text-center py-3 rounded-2xl bg-white border border-slate-200 text-slate-800 font-medium text-base"><i
                class="fas fa-book-open mr-2"></i>{{rtData.progressTitle}}</button>
            <button @click="again" v-if="rtData.allowAgain===1"
              class="text-center py-3 rounded-2xl bg-[#e72726] text-white font-semibold shadow text-base"><i
                class="fas fa-redo mr-2"></i>重新开始</button>
          </section>
        </main>
      </template> -->

      <div class="mask animate__animated animate__fadeIn" style="animation-duration: 0.5s;" v-if="show===2">
        <div class="popup score">
            <img src="img/close.png" class="close" @click="show=0">
            <div class="score-content">
                <p class="p2" v-if="rtData.login_auth===1">本次训练中</p>
                <p v-if="rtData.login_auth===1">答对 <span>{{rtData.thisTrueTotal}}</span> 题/已回答 <span>{{rtData.thisTotal}}</span> 题</p>
                <p class="p2" v-if="rtData.login_auth===0">您还没有参与训练哦</p>
            </div>
            <div class="back_container">
              <div class="back" @click="navigateTo('home')">返回首页</div>
            </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
  <script src="https://z.hfurl.cc/static/common/js/hook/hook1.js"></script>
  <script>
    const { createApp, ref, onMounted, computed, watch } = Vue;

    createApp({
      setup() {
        const currentPage = ref('home'); // home:首页, quiz:答题页, mistakes:错题本, result:成绩页
        const userName = ref('');
        const userId = ref('');
        const showModal = ref(false);

        const questions = ref([]);
        const mistakes = ref([]);
        const currentQuestionIndex = ref(0);
        const userAnswers = ref({});
        const questionResults = ref({});
        const timer = ref(0);
        const timerInterval = ref(null);
        const isSheetOpen = ref(false);
        const mistakeFilter = ref('all');
        const trainType = ref(1);
        const quizDetail = ref({});
        const rtData = ref({});

        const currentQuestion = computed(() => {
          const question = questions.value[currentQuestionIndex.value];
          // 根据问题类型初始化 userAnswers 结构
          if (question) {
            if (question.type === 'blank') {
              if (!userAnswers.value[question.id]) {
                userAnswers.value[question.id] = [''];
              }
            } else if (question.type === 'multiple') {
              if (!Array.isArray(userAnswers.value[question.id])) {
                userAnswers.value[question.id] = [];
              }
            } else if (question.type === 'single' || question.type === 'judge') {
              if (userAnswers.value[question.id] === undefined) {
                userAnswers.value[question.id] = '';
              }
            }
          }
          return question;
        });

        const formattedTimer = computed(() => {
          const minutes = String(Math.floor(timer.value / 60)).padStart(2, '0');
          const seconds = String(timer.value % 60).padStart(2, '0');
          return `${minutes}:${seconds}`;
        });

        // const resultsSummary = ref({
        //   score: 0,
        //   correctCount: 0,
        //   total: 0,
        //   time: '00:00',
        //   percentages: {
        //     single: 0,
        //     multiple: 0,
        //     judge: 0,
        //     blank: 0
        //   }
        // });

        const filteredMistakes = computed(() => {
          if (mistakeFilter.value === 'all') {
            return mistakes.value;
          }
          return mistakes.value.filter(item => item.type === mistakeFilter.value);
        });

        const badge = computed(() => {
           const q = currentQuestion.value;
           if (!q) return {};
           const result = questionResults.value[q.id];
           const answer = userAnswers.value[q.id];

           if (result === 'right') {
             return { text: '回答正确', class: 'border-green-500 text-green-700 bg-green-50' };
           }
           if (result === 'wrong') {
             return { text: '回答错误', class: 'border-rose-500 text-rose-700 bg-rose-50' };
           }
           
           if (answer && answer.length && (q.type !== 'blank' || answer[0])) {
             return { text: '已作答，待提交', class: 'border-amber-400 text-amber-700 bg-amber-50' };
           }
           return { text: '未作答', class: 'border-slate-200 text-slate-600' };
         });

        const startTimer = () => {
          if (timerInterval.value) clearInterval(timerInterval.value);
          timerInterval.value = setInterval(() => {
            timer.value++;
          }, 1000);
        };

        const stopTimer = () => {
          if (timerInterval.value) clearInterval(timerInterval.value);
        };

        const navigateTo = async (page) => {
           if (page === 'quiz') {
            //  await loadQuestions();
           } else if (page === 'mistakes') {
             await loadMistakes();
           } else if (page === 'result') {
            //  await loadResults();
           } else if (page === 'home') {
            window.location.reload();
           }
           currentPage.value = page;
         };

        const again = async () => {
          if(confirm('重新开始将清空错题库，确定开始？')){
            loadQuestions(1);
          }
        }
        // 从 PHP 后端加载问题
        const loadQuestions = async (allowAgain=0) => {
          show.value = 0;
          defaultFetch('get_questions', {
            code: rtData.value.progressCode,
            allowAgain,
            trainType: trainType.value
          }).then(data => {
            if (data.status === 1) {
              quizDetail.value = data.data.detail;
              
              // 将 PHP 问题格式转换为前端格式
              const phpQuestions = data.data.questions;
              questions.value = phpQuestions.map(q => {
                let type = 'single';
                if (q.type === 1) type = 'single';
                else if (q.type === 2) type = 'multiple';
                else if (q.type === 3) type = 'judge';
                else if (q.type === 4) type = 'blank';
                
                return {
                  id: q.id,
                  type: type,
                  title: q.tit,
                  options: q.qes || [],
                  answer: q.asw ? (typeof q.asw === 'string' ? q.asw.split('') : [q.asw]) : [],
                  analysis: q.analysis
                };
              });
              
                // 从记录中初始化用户答案
               const records = data.data.records || [];
               // 将records保存到quizDetail中，用于判断题目状态
               quizDetail.value.records = records;
               
               records.forEach((record) => {
                 // 根据题目ID查找对应的题目
                 const question = questions.value.find(q => q.id == record.questionId);
                 if (question) {
                   
                   if (question.type === 'multiple') {
                     userAnswers.value[record.questionId] = record.answer ? record.answer.split('') : [];
                   } else if (question.type === 'blank') {
                     userAnswers.value[record.questionId] = [record.answer || ''];
                   } else {
                     userAnswers.value[record.questionId] = record.answer || '';
                   }
                   
                   // 设置已答题的结果状态
                   const userAnswer = userAnswers.value[record.questionId];
                   const correctAnswer = question.answer || [];
                   let isRight = false;
                   
                   if (question.type === 'blank') {
                     // 填空题答案比较
                     const userAnswerText = Array.isArray(userAnswer) ? (userAnswer[0] || '') : (userAnswer || '');
                     isRight = userAnswerText.trim().replace(/\s+/g, '') === (correctAnswer.join('') || '').trim().replace(/\s+/g, '');
                   } else {
                     // 选择题答案比较
                     const userAnswerSet = new Set(Array.isArray(userAnswer) ? userAnswer : [userAnswer]);
                     const correctAnswerSet = new Set(correctAnswer);
                     if (userAnswerSet.size === correctAnswerSet.size) {
                       isRight = [...userAnswerSet].every(item => correctAnswerSet.has(item));
                     }
                   }
                   
                   // 设置答题结果
                   questionResults.value[record.questionId] = isRight ? 'right' : 'wrong';
                 }
               });
               
               // 设置答题结果后，跳转到上次答的最后一题的下一题（如果存在）
               if (records.length > 0) {
                 const lastRecord = records[records.length - 1];
                 const lastAnsweredIndex = questions.value.findIndex(q => q.id == lastRecord.questionId);
                 if (lastAnsweredIndex >= 0 && lastAnsweredIndex < questions.value.length - 1) {
                   currentQuestionIndex.value = lastAnsweredIndex + 1;
                 } else if (lastAnsweredIndex >= 0) {
                   // 如果已经是最后一题，则停留在最后一题
                   currentQuestionIndex.value = lastAnsweredIndex;
                 }
               }
               currentPage.value = 'quiz';
            } else {
              alert(data.msg || '加载题目失败');
            }
          }).catch(error => {
            console.error(error);
            alert('网络错误，请重试');
          });
        };

        // 从 PHP 后端加载错题
        const loadMistakes = async () => {
           defaultFetch('get_errors', {
             trainType: trainType.value
           }).then(data => {
             if (data.status === 1) {
               const phpMistakes = data.data.lists;
               const records = data.data.records || [];
               
                mistakes.value = phpMistakes.map((q) => {
                  let type = 'single';
                  if (q.type === 1) type = 'single';
                  else if (q.type === 2) type = 'multiple';
                  else if (q.type === 3) type = 'judge';
                  else if (q.type === 4) type = 'blank';
                  
                  // 从 records 数组中根据题目ID查找对应的用户答案
                  const record = records.find(r => r.questionId == q.id);
                  let userAnswer = '';
                  if (record) {
                    if (type === 'multiple') {
                      // 多选题，答案可能是字符串，需要分割成数组
                      userAnswer = record.answer ? record.answer.split('') : [];
                    } else {
                      // 单选题、判断题、填空题
                      userAnswer = record.answer || '';
                    }
                  }
                  
                  return {
                    id: q.id,
                    type: type,
                    title: q.tit,
                    options: q.qes || [],
                    answer: q.asw ? (typeof q.asw === 'string' ? q.asw.split('') : [q.asw]) : [],
                    analysis: q.analysis,
                    userAnswer: userAnswer
                  };
                });
             } else if (data.msg !== '本次训练未检测到错误题') {
               alert(data.msg || '加载错题失败');
             }
           }).catch(error => {
             console.error(error);
             alert('网络错误，请重试');
           });
         };
         
        // 从 PHP 后端加载成绩
        // const loadResults = async () => {
        //    defaultFetch('get_results', {
        //      trainType: trainType.value
        //    }).then(data => {
        //      if (data.status === 1) {
        //        resultsSummary.value = data.data;
        //      } else {
        //        alert(data.msg || '加载成绩失败');
        //      }
        //    }).catch(error => {
        //      console.error(error);
        //      alert('网络错误，请重试');
        //    });
        //  };

        watch(currentPage, (newPage) => {
          if (newPage === 'quiz') {
            startTimer();
          } else {
            stopTimer();
          }
        });

        const questionTypeLabel = (type) => {
          return { single: '单选题', multiple: '多选题', judge: '判断题', blank: '填空题' }[type] || '';
        };

        const optionKey = (i) => String.fromCharCode(65 + i);

        const getOptionClass = (q, i) => {
          const key = optionKey(i);
          const result = questionResults.value[q.id];
          const isSelected = isOptionSelected(q, i);

          if (result) { // 提交后
            if (q.answer.includes(key)) return 'border-green-500 bg-green-50';
            if (isSelected) return 'border-rose-500 bg-rose-50';
                    } else { // 提交前
            if (isSelected) return 'border-red-400 bg-red-50';
          }
          return 'hover:border-slate-400 border-slate-200';
        };

        const isOptionSelected = (q, i) => {
          const key = optionKey(i);
          const answer = userAnswers.value[q.id];
          if (q.type === 'multiple') {
            return answer && answer.includes(key);
          }
          return answer === key;
        };

        const selectOption = (q, i) => {
          if (questionResults.value[q.id]) return; // 提交后不允许修改

          const key = optionKey(i);
          const answer = userAnswers.value[q.id];

          if (q.type === 'multiple') {
            const index = answer.indexOf(key);
            if (index > -1) {
              answer.splice(index, 1);
            } else {
              answer.push(key);
            }
          } else { // 单选或判断
            userAnswers.value[q.id] = key;
          }
        };

        const submitCurrent = () => {
          return new Promise((resolve, reject) => {
            const q = currentQuestion.value;
            const rawAnswer = userAnswers.value[q.id];
            
            // 根据不同问题类型处理不同答案格式
            let answerText = '';
            if (q.type === 'blank') {
              answerText = Array.isArray(rawAnswer) ? (rawAnswer[0] || '') : (rawAnswer || '');
            } else if (q.type === 'multiple') {
              answerText = Array.isArray(rawAnswer) ? rawAnswer.join('') : (rawAnswer || '');
            } else {
              answerText = rawAnswer || '';
            }
            
            if (!answerText || (q.type === 'blank' && !answerText.trim())) {
              alert('请先选择或填写答案');
              return resolve(false); // Indicate submission was aborted
            }
            
            // 准备提交答案
            let submitAnswer = '';
            if (q.type === 'multiple') {
              submitAnswer = Array.isArray(rawAnswer) ? rawAnswer.join('') : rawAnswer;
            } else if (q.type === 'blank') {
              submitAnswer = answerText;
            } else {
              submitAnswer = rawAnswer;
            }
            
            // 检查答案是否正确
            const userAnswer = Array.isArray(rawAnswer)
              ? rawAnswer
              : (q.type === 'blank' ? [answerText] : (rawAnswer ? [rawAnswer] : []));
            const correctAnswer = q.answer || [];
            let isRight = false;
            
            if (q.type === 'blank') {
              isRight = (answerText || '').trim().replace(/\s+/g, '') === (correctAnswer.join('') || '').trim().replace(/\s+/g, '');
            } else {
              const userAnswerSet = new Set(userAnswer);
              const correctAnswerSet = new Set(correctAnswer);
              if (userAnswerSet.size === correctAnswerSet.size) {
                isRight = [...userAnswerSet].every(item => correctAnswerSet.has(item));
              }
            }
            
            defaultFetch('submit_answer', {
              turns: quizDetail.value.turns,
              trainType: quizDetail.value.trainType,
              sign: quizDetail.value.sign,
              pick: submitAnswer,
              id: q.id,
              right: isRight ? 'true' : 'false'
            }).then(data => {
              if (data.status >= 1) {
                questionResults.value[q.id] = isRight ? 'right' : 'wrong';
                if (data.status === 2) {
                  show.value = 2;
                }
              } else {
                alert(data.msg || '提交失败');
              }
              resolve(true); // Indicate submission was successful
            }).catch(error => {
              console.error(error);
              alert('网络错误，请重试');
              reject(error);
            });
          });
        };

        const prevQuestion = () => {
          if (currentQuestionIndex.value > 0) {
            currentQuestionIndex.value--;
          }
        };

        const nextQuestion = async () => {
          let canAdvance = true;
          if (canAdvance && currentQuestionIndex.value < questions.value.length - 1) {
            currentQuestionIndex.value++;
          }
        };

        const selectQuestion = (index) => {
          currentQuestionIndex.value = index;
          isSheetOpen.value = false;
        };

        const getSheetButtonClass = (q, i) => {
           const result = questionResults.value[q.id];
           const answer = userAnswers.value[q.id];
           const isAnswered = answer && answer.length > 0 && (q.type !== 'blank' || answer[0]);
           const isCurrent = i === currentQuestionIndex.value;

           let cls = '';
           if (result === 'right') cls = 'bg-green-50 border-green-500 text-green-700';
           else if (result === 'wrong') cls = 'bg-rose-50 border-rose-500 text-rose-700';
           else if (isAnswered) cls = 'bg-[#fff7e6] border-[#f6a61d] text-[#a56300]';
           else cls = 'bg-white border-slate-200 text-slate-600';

           if (isCurrent) cls += ' ring-2 ring-[#f6a61d]';
           return cls;
         };

        const formatAnswer = (val) => {
          if (Array.isArray(val)) return val.filter(Boolean).join('');
          if (typeof val === 'string') return val || '—';
          return '—';
        };

        const handleConfirm = async () => {
          if (!userName.value) {
            alert('请填写姓名');
            return;
          }
          const reg = /^\d{6}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
          if (!reg.test(userId.value.trim())) {
            alert('身份证号格式不正确，请检查后重试');
            return;
          }
          
          defaultFetch('authenticate', {
            name: userName.value,
            idcard: userId.value.toUpperCase()
          }).then(data => {
            if (data.status === 1) {
              localStorage.setItem('wsdtUserName', userName.value);
              localStorage.setItem('wsdtUserId', userId.value.toUpperCase());
              showModal.value = false;
              alert(data.msg);
            } else {
              alert(data.msg);
            }
          }).catch(error => {
            console.error(error);
            alert('网络错误，请重试');
          });
        };

        onMounted(async () => {
          await checkUserStatus();
          await checkTrainStatus();
        });

        // 检查用户认证状态
        const checkUserStatus = async () => {
          defaultFetch('user_status', {}).then(data => {
            if (data.status === 1 && data.data.isLogin) {
              showModal.value = false;
              // 如果只有一个可用的训练类型，则设置训练类型
              if (data.data.trainList.length === 1) {
                trainType.value = data.data.trainList[0].class;
              }
            } else {
              showModal.value = true;
            }
          }).catch(error => {
            showModal.value = true;
          });
        };

        // 检查训练状态
        const checkTrainStatus = async () => {
          defaultFetch('train_status', {
            class: trainType.value
          }).then(data => {
            if (data.status === 1) {
              rtData.value = data.data;
            }
          }).catch(error => {
            console.error(error);
          });
        };
        const show = ref(0);
        watch(show, (newVal) => {
          if (newVal === 2) {
            checkTrainStatus();
          }
        });
        return {
          currentPage,
          navigateTo,
          rtData,
          again,
          userName,
          userId,
          showModal,
          handleConfirm,
          questions,
          mistakes,
          currentQuestionIndex,
          userAnswers,
          questionResults,
          formattedTimer,
          isSheetOpen,
          currentQuestion,
          badge,
          loadQuestions,
          questionTypeLabel,
          optionKey,
          getOptionClass,
          selectOption,
          isOptionSelected,
          submitCurrent,
          prevQuestion,
          nextQuestion,
          selectQuestion,
          getSheetButtonClass,
          mistakeFilter,
          filteredMistakes,
          // resultsSummary,
          formatAnswer,
          // loadResults,
          show,
        };
      }
    }).mount('#app');
  </script>
  {include file="share"/}
</body>

</html>