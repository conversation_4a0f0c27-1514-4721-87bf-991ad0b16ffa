html,
body,
p,
div,
li,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 3.5vw;
}
a {
  text-decoration: none;
  display: block;
  font-size: 100%;
  color: #333;
}
li:hover {
  cursor: pointer;
}
h1 {
  display: none;
}
[v-cloak] {
  display: none;
}
.warp {
  min-height: 177.867vw;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #dd1817 url(../img/bj.jpg) no-repeat center center / 100% auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 10vw 0 40vw;
}
.warp .logo {
  position: absolute;
  top: 8.667vw;
  left: 8vw;
  width: 26.8vw;
}
.warp .icon {
  position: absolute;
  top: 0;
  right: 8vw;
  width: 21vw;
}
.warp .title {
  width: 82.2667vw;
  flex-shrink: 0;
}
.warp .new_title {
  color: #fff;
  font-weight: bold;
  font-size: 8vw;
  width: 90vw;
  text-align: center;
}
.warp .bg1 {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100vw;
  pointer-events: none;
}
.warp .button-container {
  position: absolute;
  top: 7.2vw;
  right: 0;
}
.warp .button-container p {
  margin-bottom: 4vw;
  width: 26vw;
  height: 8.133vw;
  background: #F0474C;
  border: 0.267vw solid #FFFAF3;
  border-right: none;
  border-radius: 4vw 0 0 4vw;
  font-size: 4.667vw;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .button1 {
  width: 40vw;
}
.warp .start {
  margin-top: 10vw;
  width: 50.4vw;
  height: 12.933vw;
  background: url(../img/start0.png) no-repeat center bottom / 100% 100%;
  font-size: 7vw;
  text-align: center;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 2vw;
  background: #FEA12A;
  border-radius: 6.5vw;
  height: 13vw;
  padding-bottom: 0;
  box-shadow: 0vw 1.467vw 0.4vw 0vw rgba(0, 0, 0, 0.28);
}
.warp .form {
  width: 80vw;
  height: 72.8vw;
  margin-top: 36.133vw;
  background: url(../img/form.png) no-repeat center top / 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 18.667vw;
}
.warp .form input {
  width: 64vw;
  height: 10.667vw;
  background: #EBEBEB;
  border-radius: 5.333vw;
  margin-bottom: 3.6vw;
  font-size: 3.5vw;
  padding-left: 4.267vw;
}
.warp .form input::placeholder {
  color: #999;
}
.warp .form .button {
  width: 64vw;
  height: 13.333vw;
  background: linear-gradient(-270deg, #ED5F6A 0%, #F7B360 100%);
  border-radius: 6.667vw;
  font-size: 5.067vw;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
}
.warp .dt-container {
  position: relative;
  z-index: 0;
  margin-top: 20vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .dt-container .dt1 {
  width: 47.333vw;
}
.warp .dt-container .dt-area {
  margin: -6vw 0 0;
  padding: 0 4.333vw;
  padding-bottom: 6vw;
  width: 90vw;
  background: #FFFFFF;
  box-shadow: -0.133vw 1.6vw 2.4vw 0vw rgba(8, 96, 137, 0.45);
  border-radius: 3.6vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .dt-container .dt-area .dt-data {
  margin-top: -7vw;
  width: 37vw;
  height: 13.333vw;
  background: linear-gradient(0deg, #F58A18 0%, #F6B420 100%);
  box-shadow: 0.4vw 0.933vw 4.667vw 0.4vw rgba(245, 156, 27, 0.24);
  border-radius: 0vw 0vw 3.733vw 3.733vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .dt-container .dt-area .dt-data p:nth-of-type(1) {
  margin-top: 3.333vw;
  font-size: 5.067vw;
  font-weight: 500;
  color: #FFFFFF;
}
.warp .dt-container .dt-area .dt-data p:nth-of-type(2) {
  font-size: 3.5vw;
  font-weight: 500;
  color: #FFFFFF;
}
.warp .dt-container .dt-area .dt-content {
  width: 100%;
}
.warp .dt-container .dt-area .dt-content .question {
  margin-top: 7.733vw;
  font-size: 5.067vw;
  font-weight: 500;
  color: #333333;
  line-height: 7.733vw;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.warp .dt-container .dt-area .dt-content .answer .pick {
  background-color: #e0e0e0;
}
.warp .dt-container .dt-area .dt-content .answer .cur {
  background-color: #07c160;
}
.warp .dt-container .dt-area .dt-content .answer .cur .left {
  background-color: #fff;
  color: #07c160;
}
.warp .dt-container .dt-area .dt-content .answer .cur .right {
  color: #fff;
}
.warp .dt-container .dt-area .dt-content .answer .err {
  background-color: #ee0a24;
}
.warp .dt-container .dt-area .dt-content .answer .err .left {
  background-color: #fff;
  color: #ee0a24;
}
.warp .dt-container .dt-area .dt-content .answer .err .right {
  color: #fff;
}
.warp .dt-container .dt-area .dt-content .answer .cur-icon {
  padding-right: 10vw;
  position: relative;
}
.warp .dt-container .dt-area .dt-content .answer .cur-icon::after {
  width: 7vw;
  height: 7vw;
  content: '';
  background: url(../img/cur-icon.png) no-repeat center center / 100% auto;
  position: absolute;
  right: 1.5vw;
}
.warp .dt-container .dt-area .dt-content .answer .err-icon {
  padding-right: 10vw;
  position: relative;
}
.warp .dt-container .dt-area .dt-content .answer .err-icon::after {
  width: 7vw;
  height: 7vw;
  content: '';
  background: url(../img/err-icon.png) no-repeat center center / 100% auto;
  position: absolute;
  right: 1.5vw;
}
.warp .dt-container .dt-area .answer-asw {
  margin-top: 4vw;
  color: #ee0a24;
}
.warp .dt-container .dt-area .answer-asw2 {
  margin-top: 4vw;
  color: #07c160;
}
.warp .dt-container .dt-area .button_area {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 6.667vw;
  width: 100%;
}
.warp .dt-container .dt-area .next {
  margin: 0 2.667vw;
  flex: 1;
  height: 13.333vw;
  background: linear-gradient(90deg, #EE6569 0%, #F7B060 100%);
  border-radius: 6.667vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 5.067vw;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 5.333vw;
}
.warp .back {
  margin: 6.667vw 0 0;
  width: 70.667vw;
  height: 13.333vw;
  background: linear-gradient(90deg, #EE6569 0%, #F7B060 100%);
  border-radius: 6.667vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 5.067vw;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 5.333vw;
  z-index: 2;
  position: absolute;
  bottom: 5vw;
}
.warp2 {
  padding: 6vw 0 20vw;
}
.warp3 {
  padding: 4vw 0;
  overflow-y: auto;
}
.tit2 {
  color: #fff;
  font-size: 5vw;
  letter-spacing: 0.5vw;
  text-align: center;
  width: 80vw;
  display: block;
  position: absolute;
  top: 7vw;
  font-weight: bold;
}
.index2 {
  justify-content: center;
}
.index2 .tit {
  margin: -15vw 0 5vw;
  color: #ffdc98;
  font-size: 8vw;
  letter-spacing: 1vw;
  text-align: center;
  width: 80vw;
  display: block;
}
.index2 .form2 {
  margin-top: 0 !important;
}
.index2 .van-popup--bottom {
  top: 50%;
  bottom: auto;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  width: 80%;
  min-height: 60%;
}
.index2 .van-popup--bottom .van-action-sheet__description {
  font-size: 5.5vw;
}
.index2 .van-popup--bottom .van-action-sheet__name {
  font-size: 5vw;
}
.index2 .van-popup--bottom.van-popup--round {
  border-radius: 16px;
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
.mask .popup {
  width: 64vw;
  height: 76vw;
  background: #FFFFFF;
  border-radius: 3.6vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.mask .popup .button {
  width: 26.667vw;
  height: 9vw;
  margin-top: -4.4vw;
}
.mask .popup .close {
  width: 9.067vw;
  height: 9.067vw;
  position: absolute;
  bottom: -16vw;
}
.mask .popup .back_container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.mask .popup .back_container .back {
  margin-top: 2.8vw;
  width: 48vw;
  height: 10.667vw;
  background: linear-gradient(90deg, #F58A18 0%, #F6B420 100%);
  border-radius: 5.333vw;
  font-size: 5.067vw;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mask .popup .popup-content {
  margin-top: 5.067vw;
  padding: 0 4.4vw;
  width: 100%;
  height: 44.667vw;
  overflow-y: scroll;
  font-size: 3.5vw;
  color: #666666;
  line-height: 6.4vw;
}
.mask .popup .popup-content p {
  font-size: 3.5vw;
  color: #666666;
  line-height: 6.4vw;
}
.mask .popup .popup-content div {
  font-size: 3.5vw;
  color: #666666;
  line-height: 6.4vw;
}
.mask .score {
  background: url(../img/popup.png?v=1) no-repeat center top / 100% 100%;
  width: 85vw;
  height: 89.8vw;
  border-radius: 3.6vw;
  margin-top: -10vw;
}
.mask .score .score-content {
  margin-top: 37.6vw;
}
.mask .score .score-content p {
  font-size: 4vw;
  color: #333333;
  line-height: 7.733vw;
  text-align: center;
}
.mask .score .score-content p span {
  font-size: 5.067vw;
  color: #DF5B5E;
}
.mask .score .score-content .p2 {
  font-size: 6vw;
  margin-bottom: 4vw;
}
.mask .score .back {
  width: 48vw;
  height: 10.667vw;
  background: linear-gradient(90deg, #F58A18 0%, #F6B420 100%);
  border-radius: 5.333vw;
  margin-top: 3vw;
}
@keyframes pulsate-bck {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}
