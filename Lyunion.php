<?php
namespace app\zt2025\controller;

use app\BaseController;

use app\common\model\Wechat;
use app\common\controller\ZtController;
use app\common\controller\VoteController;
use think\facade\View;
use think\facade\Db;
use think\facade\Cache;

use wechat\Jsauth;
use wechat\Jssdk;

class Lyunion extends VoteController
{
    private $log_fid = 509;//训练记录表

    private $user_fid = 510;//学员信息表

    private $dirname = 'lyunion';//专题目录

    private $trainList = array(
        1 => '社工技能竞赛训练',
    );

    protected $keyCofig = []; //redis key数组

	public function initialize()
    {
        parent::initialize();
        $this->fid = 508;//题库表
        $this->config_id = 225;//专题微信配置id

        $this->tableName = 'moo_form_data_2025_2';
        $this->session_id = '2025_'.$this->dirname.'_' . $this->fid;//session键值;
        $this->getConfig(); //获取微信配置信息

        //测试授权
        $this->ztdebug($this->dirname);
        //授权，key和测试授权保持一致才能起作用
        $this->Oauth('', 0, $this->dirname);

        //统计
        totalviews_new($this->fid);

//        if($this->wechat_user['openid'] == 'oa2Bo1HyHiTuOkgf8rlZryzdXn74'){
//            $this->configWx['reg_end_time'] = time() + 60;
//        }

        //活动状态
        if (time() < $this->configWx['reg_start_time']) {
            $this->endtime = 1;
        } elseif (time() > $this->configWx['reg_end_time']) {
            $this->endtime = 2;
        }

    }


    public function index2()
    {
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_3,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_6', 1)
            ->where('openid', $this->wechat_user['openid'])
            ->order('id desc')
            ->select()->toArray();
        $trainArray = array();
        $isLogin = 0;
        if($userList){
            $isLogin = 1;
            //针对已认证的 存在未认证的同步认证 暂时不做处理
            /*
            $finally = Db::name($this->tableName)
                ->field('id')
                ->where('fid', $this->user_fid)
                ->where('key_6', 1)
                ->where('key_1', $userList[0]['key_1'])
                ->where('key_3', $userList[0]['key_3'])
                ->order('id desc')
                ->find();
            */

            if(count($userList) == 1){
                header("location:page?class={$userList[0]['key_int3']}");
                exit;
            }
            $trainList = $this->trainList;
            foreach ($userList as $v)
            {
                $trainArray[] = array(
                    'name' => $trainList[$v['key_int3']],
                    'url'  => 'page?class='.$v['key_int3']
                );
            }
        } else {
            $insertData = array(
                'fid' => $this->user_fid,
                'openid' => $this->wechat_user['openid'],
                'key_1' => '',
                'key_3' => '',
                'key_int1' => 1,
                'key_int2' => 0,
                'key_int3' => 1,
                'key_5' => date('Y-m-d H:i:s'),
                'key_6' => 1,
                'addtime' => time()
            );
            $res = Db::name($this->tableName)->save($insertData);
            if($res){
                header("location:page?class=1");
                exit;
            }
        }
        View::assign('isLogin', $isLogin);
        View::assign('trainList', json_encode($trainArray,256));
        View::assign('userList', $userList);
        return view();
    }
    public function index()
    {
        return view();
    }
    
    // 用户认证处理
    public function authenticate()
    {
        $returnMsg = array();
        $returnMsg['status'] = 0;
        
        if ($this->endtime == 1) {
            $returnMsg['msg'] = '训练暂未开始';
            echo json_encode($returnMsg);
            exit;
        }
        if ($this->endtime == 2) {
            $returnMsg['msg'] = '训练已结束';
            echo json_encode($returnMsg);
            exit;
        }
        
        $name   = getgpc_new($_POST['name']);
        $idcard = getgpc_new($_POST['idcard']);
        $idcard = strtoupper($idcard);

        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_1', $name)
            ->where('key_3', $idcard)
            ->where('key_6', 1)
            ->order('id desc')
            ->select()->toArray();
            
        if(!$userList){
            $returnMsg['msg'] = '未找到您的信息，请核对重试！';
            echo json_encode($returnMsg);exit;
        }

        $ids = $trainArray = array();
        $trainList = $this->trainList;
        foreach ($userList as $v)
        {
            $trainArray[] = array(
                'name' => $trainList[$v['key_int3']],
                'url'  => 'page?class='.$v['key_int3']
            );
            if(!$v['openid']){
                $ids[] = $v['id'];
            }
        }
        
        $update = array(
            'openid' => $this->wechat_user['openid'],
            'key_5'  => date('Y-m-d H:i:s'),
            'key_int1' => 1
        );
        $res = Db::name($this->tableName)
            ->where('fid', $this->user_fid)
            ->where('id', 'in', $ids)
            ->update($update);
            
        if ($res) {
            $returnMsg['status'] = 1;
            $returnMsg['trainList'] = $trainArray;
            $returnMsg['url'] = $trainArray[0]['url'];
            $returnMsg['msg'] = '信息认证成功！';
            echo json_encode($returnMsg);
            exit;
        } else {
            $returnMsg['msg'] = '信息认证失败！';
            echo json_encode($returnMsg);
            exit;
        }
    }
    
    // 开始答题处理
    public function start_quiz()
    {
        $returnMsg = array();
        $returnMsg['status'] = 0;
        
        if ($this->endtime == 1) {
            $returnMsg['msg'] = '训练暂未开始';
            echo json_encode($returnMsg);
            exit;
        }
        if ($this->endtime == 2) {
            $returnMsg['msg'] = '训练已结束';
            echo json_encode($returnMsg);
            exit;
        }
        
        $code = intval($_POST['code']);
        $allowAgain = intval($_POST['allowAgain']);//是否重新开始
        $classType  = intval($_POST['classType']);
        if(!in_array($classType,array(1,2))){
            $returnMsg['msg'] = '训练类型异常';
            echo json_encode($returnMsg);
            exit;
        }

        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int2,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $classType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
            
        if(!$userList){
            $returnMsg['msg'] = '未找到您的信息，或已超出训练时间！';
            echo json_encode($returnMsg);exit;
        }
        
        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            $returnMsg['msg'] = '抱歉，您已超出本次训练时间！';
            echo json_encode($returnMsg);exit;
        }

        if($allowAgain == 1){
            $update = array(
                'key_int1' => Db::raw('key_int1+1'),
                'key_int2' => 0
            );
            $update = Db::name($this->tableName)
                ->where('fid', $this->user_fid)
                ->where('key_int3', $classType)
                ->where('openid', $this->wechat_user['openid'])
                ->where('key_6', 1)
                ->update($update);
            if(!$update){
                $returnMsg['status'] = -1;
                $returnMsg['msg'] = '网络忙，请稍后重试';
                echo json_encode($returnMsg);die;
            }
        }else{
            if($code == 2){
                if($userList['key_int1'] && $userList['key_int2'] != 0){
                    $update = array(
                        'key_int1' => Db::raw('key_int1+1'),
                        'key_int2' => 0
                    );
                    $update = Db::name($this->tableName)
                        ->where('fid', $this->user_fid)
                        ->where('key_int3', $classType)
                        ->where('openid', $this->wechat_user['openid'])
                        ->where('key_6', 1)
                        ->update($update);
                    if(!$update){
                        $returnMsg['status'] = -1;
                        $returnMsg['msg'] = '网络忙，请稍后重试';
                        echo json_encode($returnMsg);die;
                    }
                }
            }
        }

        $returnMsg['status'] = 1;
        $returnMsg['msg'] = 'success';
        echo json_encode($returnMsg);die;
    }
    
    // 获取题目数据
    public function get_questions()
    {
        $trainType = intval($_POST['trainType']);
        $allowAgain = intval($_POST['allowAgain']);//是否重新开始
        if(!in_array($trainType,array(1,2))){
            $trainType = 1;
        }
        
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int2,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
            
        if(!$userList){
            echo json_encode(['status' => 0, 'msg' => '未找到用户信息']);
            exit;
        }

        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            echo json_encode(['status' => 0, 'msg' => '抱歉，您已超出本次训练时间！']);
            exit;
        }
        $thisRound = $userList['key_int1'];//当前轮次
        if($allowAgain == 1){
            //重新开始
            $update = array(
                'key_int1' => Db::raw('key_int1+1'),
                'key_int2' => 0
            );
            $update = Db::name($this->tableName)
                ->where('fid', $this->user_fid)
                ->where('key_int3', $trainType)
                ->where('openid', $this->wechat_user['openid'])
                ->where('key_6', 1)
                ->update($update);
            if(!$update){
                $result['status'] = -1;
                $result['msg'] = '网络忙，请稍后重试';
                echo json_encode($result);die;
            }
            $thisRound += 1;
        }

        $loginSign = $userList['id'].'#'.$userList['openid'];
        $thisTotal = Db::name($this->tableName)
            ->where('fid', $this->log_fid)
            ->where('key_1', $loginSign)
            ->where('key_int2', $thisRound)
            ->where('key_int3', $trainType)
            ->count();
        $nextQuestion = $thisTotal + 1;

        $recordList = array();
        if($nextQuestion > 1){
            //读取所有答题答案，包含题目ID
            $logList = Db::name($this->tableName)
                ->field('key_3,key_int1')
                ->where('fid', $this->log_fid)
                ->where('key_1', $loginSign)
                ->where('key_int2', $thisRound)
                ->where('key_int3', $trainType)
                ->order('id asc')
                ->select()->toArray();
            foreach ($logList as $val) {
                $recordList[] = array(
                    'questionId' => $val['key_int1'],
                    'answer' => $val['key_3']
                );
            }
        }

        $questions = $this->getQuestion($trainType);
        $detail = array(
            'sign' => $loginSign,
            'trainType' => $trainType,
            'turns' => $thisRound,
            'subtitle' => $this->trainList[$trainType]
        );

        echo json_encode([
            'status' => 1,
            'data' => [
                'detail' => $detail,
                'nextQuestion' => $nextQuestion,
                'questions' => $questions,
                'records' => $recordList,
            ]
        ]);
        exit;
    }
    
    // 提交答案处理
    public function submit_answer()
    {
        $result = array(
            'status' => 0
        );
        
        $turns = intval($_POST['turns']);
        $trainType = intval($_POST['trainType']);
        $sign = getgpc_new($_POST['sign']);

        $option = getgpc_new($_POST['pick']);
        $id = intval($_POST['id']);
        $right = $_POST['right'];
        $is_true = $right == 'true' ? 1 : 0;

        $signData = explode('#', $sign);
        $uid = intval($signData[0]);
        if($signData[1] != $this->wechat_user['openid'] || !$uid){
            $result['msg'] = '授权异常';
            echo json_encode($result);
            exit;
        }
        
        $exist = Db::name($this->tableName)
            ->where('fid', $this->log_fid)
            ->where('key_1', $sign)
            ->where('key_int2', $turns)
            ->where('key_int3', $trainType)
            ->where('key_int1', $id)
            ->count();

        if(!$exist){
            $status = 1;
            $last = Db::name($this->tableName)
                ->field('id')
                ->where('fid', $this->fid)
                ->where('key_int1', $trainType)
                ->where('key_int3', 1)
                ->order('id desc')
                ->find();
            if($last['id'] == $id){
                $status = 2;
                $thisTrueTotal = Db::name($this->tableName)
                    ->where('fid', $this->log_fid)
                    ->where('key_1', $sign)
                    ->where('key_int2', $turns)
                    ->where('key_int3', $trainType)
                    ->where('key_2', 1)
                    ->count();
                $result['correctNum'] = $thisTrueTotal;
            }
            
            $insertData = array(
                'openid' => $this->wechat_user['openid'],
                'key_1'  => $sign,
                'key_2'  => $is_true,
                'key_3'  => $option,
                'key_int1' => $id,
                'key_int2' => $turns,
                'key_int3' => $trainType,
                'fid' => $this->log_fid,
                'addtime' => time()
            );
            $res = Db::name($this->tableName)->save($insertData);
            if($res){
                $update = array(
                    'key_int2' => Db::raw('key_int2+1'),
                    'key_4'    => date('Y-m-d H:i:s')
                );
                Db::name($this->tableName)
                    ->where('fid', $this->user_fid)
                    ->where('id', $uid)
                    ->where('openid', $this->wechat_user['openid'])
                    ->update($update);
                $result['status'] = $status;
                $result['msg'] = '保存成功';
            }else{
                $result['status'] = 0;
                $result['msg'] = '保存失败';
            }
        }else{
            //已答题的也通过
            $result['status'] = 1;
            $result['msg'] = '重复答题';
        }
        echo json_encode($result);
        die;
    }
    
    // 获取错题数据
    public function get_errors()
    {
        $trainType = intval($_POST['trainType']);
        if(!in_array($trainType,array(1,2))){
            echo json_encode(['status' => 0, 'msg' => '训练类型异常']);
            exit;
        }
        
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
            
        if(!$userList){
            echo json_encode(['status' => 0, 'msg' => '未找到用户信息']);
            exit;
        }
        
        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            echo json_encode(['status' => 0, 'msg' => '抱歉，您已超出本次训练时间！']);
            exit;
        }

        //读取错题
        $loginSign = $userList['id'].'#'.$userList['openid'];
        $wrongList = Db::name($this->tableName)
            ->field('key_3,key_int1')
            ->where('fid', $this->log_fid)
            ->where('key_int3', $trainType)
            ->where('key_1', $loginSign)
            ->where('key_int2', $userList['key_int1'])
            ->where('key_2', 0)
            ->order('id asc')
            ->select()->toArray();
            
        if(!$wrongList){
            echo json_encode(['status' => 0, 'msg' => '本次训练未检测到错误题']);
            exit;
        }

        $wData = array();
        foreach ($wrongList as $v){
            $wData[$v['key_int1']] = $v['key_3'];
        }
        
        $questions = $this->getQuestion($trainType);
        $lists = $recordList = array();
        foreach ($questions as $v)
        {
            if(array_key_exists($v['id'], $wData)){
                // 保持与 get_questions 方法一致的 records 结构
                $recordList[] = array(
                    'questionId' => $v['id'],
                    'answer' => $wData[$v['id']]
                );
                $lists[] = $v;
            }
        }

        echo json_encode([
            'status' => 1,
            'data' => [
                'subtitle' => $this->trainList[$trainType],
                'lists' => $lists,
                'records' => $recordList
            ]
        ]);
        exit;
    }
    
    // 获取用户状态
    public function user_status()
    {
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_3,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_6', 1)
            ->where('openid', $this->wechat_user['openid'])
            ->order('id desc')
            ->select()->toArray();
        $trainArray = array();
        $isLogin = 0;
        if($userList){
            $isLogin = 1;
            if(count($userList) == 1){
                $trainArray[] = array(
                    'name' => $this->trainList[$userList[0]['key_int3']],
                    'class' => $userList[0]['key_int3']
                );
            } else {
                $trainList = $this->trainList;
                foreach ($userList as $v)
                {
                    $trainArray[] = array(
                        'name' => $trainList[$v['key_int3']],
                        'class' => $v['key_int3']
                    );
                }
            }
        }else{
            $insertData = array(
                'fid' => $this->user_fid,
                'openid' => $this->wechat_user['openid'],
                'key_1' => '',
                'key_3' => '',
                'key_int1' => 1,
                'key_int2' => 0,
                'key_int3' => 1,
                'key_5' => date('Y-m-d H:i:s'),
                'key_6' => 1,
                'addtime' => time()
            );
            $res = Db::name($this->tableName)->save($insertData);
            if($res !== false){
                $isLogin = 1;
                $trainArray[] = array(
                    'name' => $this->trainList[$insertData['key_int3']],
                    'class' => $insertData['key_int3']
                );
            }
        }
        
        echo json_encode([
            'status' => 1,
            'data' => [
                'isLogin' => $isLogin,
                'trainList' => $trainArray,
//                'userList' => $userList
            ]
        ]);
        exit;
    }
    
    // 获取训练状态
    public function train_status()
    {
        $trainType = intval($_POST['class']);
        if(!in_array($trainType,array(1,2))){
            $trainType = 1;
        }
        
        $rtData = $this->trainStatus($trainType);
        echo json_encode([
            'status' => 1,
            'data' => $rtData
        ]);
        exit;
    }
    
    // 获取成绩数据
    public function get_results()
    {
        $trainType = intval($_POST['trainType']);
        if(!in_array($trainType,array(1,2))){
            $trainType = 1;
        }
        
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
            
        if(!$userList){
            echo json_encode(['status' => 0, 'msg' => '未找到用户信息']);
            exit;
        }
        
        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            echo json_encode(['status' => 0, 'msg' => '抱歉，您已超出本次训练时间！']);
            exit;
        }

        // 获取答题记录
        $loginSign = $userList['id'].'#'.$userList['openid'];
        $logList = Db::name($this->tableName)
            ->field('key_2,key_int1')
            ->where('fid', $this->log_fid)
            ->where('key_1', $loginSign)
            ->where('key_int2', $userList['key_int1'])
            ->where('key_int3', $trainType)
            ->order('id asc')
            ->select()->toArray();
            
        // 获取题目总数和题型分布
        $questions = $this->getQuestion($trainType);
        $totalQuestions = count($questions);
        $correctCount = 0;
        
        // 统计各题型数量和正确数量
        $typeStats = [
            'single' => ['total' => 0, 'correct' => 0],
            'multiple' => ['total' => 0, 'correct' => 0],
            'judge' => ['total' => 0, 'correct' => 0],
            'blank' => ['total' => 0, 'correct' => 0]
        ];
        
        foreach ($questions as $q) {
            $type = '';
            if ($q['type'] == 1) $type = 'single';
            else if ($q['type'] == 2) $type = 'multiple';
            else if ($q['type'] == 3) $type = 'judge';
            else if ($q['type'] == 4) $type = 'blank';
            
            $typeStats[$type]['total']++;
            
            // 查找这道题的答题记录
            $answerRecord = null;
            foreach ($logList as $log) {
                if ($log['key_int1'] == $q['id']) {
                    $answerRecord = $log;
                    break;
                }
            }
            
            if ($answerRecord && $answerRecord['key_2'] == 1) {
                $correctCount++;
                $typeStats[$type]['correct']++;
            }
        }
        
        // 计算各题型正确率
        $percentages = [];
        foreach ($typeStats as $type => $stats) {
            $percentages[$type] = $stats['total'] > 0 ? round(($stats['correct'] / $stats['total']) * 100) : 0;
        }
        
        // 计算总分
        $score = $totalQuestions > 0 ? round(($correctCount / $totalQuestions) * 100) : 0;
        
        // 获取用时（这里需要从用户表获取开始时间，暂时返回0）
        $timeUsed = 0;
        
        $resultData = [
            'score' => $score,
            'correctCount' => $correctCount,
            'total' => $totalQuestions,
            'time' => $timeUsed,
            'percentages' => $percentages,
            'typeStats' => $typeStats
        ];
        
        echo json_encode([
            'status' => 1,
            'data' => $resultData
        ]);
        exit;
    }

    //认证
    public function sub()
    {
        $returnMsg = array();
        $returnMsg['status'] = 0;
        if ($this->endtime == 1) {
            $returnMsg['msg'] = '训练暂未开始';
            echo json_encode($returnMsg);
            exit;
        }
        if ($this->endtime == 2) {
            $returnMsg['msg'] = '训练已结束';
            echo json_encode($returnMsg);
            exit;
        }
        $name   = getgpc_new($_POST['name']);
        $idcard = getgpc_new($_POST['idcard']);
        $idcard = strtoupper($idcard);

        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_1', $name)
            ->where('key_3', $idcard)
            ->where('key_6', 1)
            ->order('id desc')
            ->select()->toArray();
        if(!$userList){
            $returnMsg['msg'] = '未找到您的信息，请核对重试！';
            echo json_encode($returnMsg);exit;
        }

        $ids = $trainArray = array();
        $trainList = $this->trainList;
        foreach ($userList as $v)
        {
            $trainArray[] = array(
                'name' => $trainList[$v['key_int3']],
                'url'  => 'page?class='.$v['key_int3']
            );
            if(!$v['openid']){
                $ids[] = $v['id'];
            }
        }
        $update = array(
            'openid' => $this->wechat_user['openid'],
            'key_5'  => date('Y-m-d H:i:s'),
            'key_int1' => 1
        );
        $res = Db::name($this->tableName)
            ->where('fid', $this->user_fid)
            ->where('id', 'in', $ids)
            ->update($update);
        if ($res) {
            $returnMsg['status'] = 1;
            $returnMsg['trainList'] = $trainArray;
            $returnMsg['url'] = $trainArray[0]['url'];
            $returnMsg['msg'] = '信息认证成功！';
            echo json_encode($returnMsg);
            exit;
        } else {
            $returnMsg['msg'] = '信息认证失败！';
            echo json_encode($returnMsg);
            exit;
        }
    }

    public function page()
    {
        $trainType = intval($_GET['class']);
        if(!in_array($trainType,array(1,2))){
            $trainType = 1;
        }
        $rtData = $this->trainStatus($trainType);
        if(!$rtData['login_auth']){
            header('location:index');
            exit;
        }

        View::assign('rtData', $rtData);
        return view();
    }

    public function start()
    {
        $returnMsg = array();
        $returnMsg['status'] = 0;
        if ($this->endtime == 1) {
            $returnMsg['msg'] = '训练暂未开始';
            echo json_encode($returnMsg);
            exit;
        }
        if ($this->endtime == 2) {
            $returnMsg['msg'] = '训练已结束';
            echo json_encode($returnMsg);
            exit;
        }
        $code = intval($_POST['code']);
        $allowAgain = intval($_POST['allowAgain']);//是否重新开始
        $classType  = intval($_POST['classType']);
        if(!in_array($classType,array(1,2))){
            $returnMsg['msg'] = '训练类型异常';
            echo json_encode($returnMsg);
            exit;
        }

        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int2,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $classType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
        if(!$userList){
            $returnMsg['msg'] = '未找到您的信息，或已超出训练时间！';
            echo json_encode($returnMsg);exit;
        }
        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            $returnMsg['msg'] = '抱歉，您已超出本次训练时间！';
            echo json_encode($returnMsg);exit;
        }

        if($allowAgain == 1){
            $update = array(
                'key_int1' => Db::raw('key_int1+1'),
                'key_int2' => 0
            );
            $update = Db::name($this->tableName)
                ->where('fid', $this->user_fid)
                ->where('key_int3', $classType)
                ->where('openid', $this->wechat_user['openid'])
                ->where('key_6', 1)
                ->update($update);
            if(!$update){
                $result['status'] = -1;
                $result['msg'] = '网络忙，请稍后重试';
                echo json_encode($result);die;
            }
        }else{
            if($code == 2){
                if($userList['key_int1'] && $userList['key_int2'] != 0){
                    $update = array(
                        'key_int1' => Db::raw('key_int1+1'),
                        'key_int2' => 0
                    );
                    $update = Db::name($this->tableName)
                        ->where('fid', $this->user_fid)
                        ->where('key_int3', $classType)
                        ->where('openid', $this->wechat_user['openid'])
                        ->where('key_6', 1)
                        ->update($update);
                    if(!$update){
                        $result['status'] = -1;
                        $result['msg'] = '网络忙，请稍后重试';
                        echo json_encode($result);die;
                    }
                }
            }
        }

        $result['status'] = 1;
        $result['msg'] = 'success';
        echo json_encode($result);die;
    }

    public function dt()
    {
        $trainType = intval($_GET['class']);
        if(!in_array($trainType,array(1,2))){
            $trainType = 1;
        }
        /*
         * user表 key_int1轮次 key_int2题数 key_int3等级
         */
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int2,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
        if(!$userList){
            header('location:index');
            exit;
        }

        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            echo '<script>alert("抱歉，您已超出本次训练时间！");window.history.go(-1);</script>';
            exit;
        }

        $loginSign = $userList['id'].'#'.$userList['openid'];
        $thisTotal = Db::name($this->tableName)
            ->where('fid', $this->log_fid)
            ->where('key_1', $loginSign)
            ->where('key_int2', $userList['key_int1'])
            ->where('key_int3', $trainType)
            ->count();
        $nextQuestion = $thisTotal + 1;

        $recordList = array();
        if($nextQuestion > 1){
            //读取所有答题答案，包含题目ID
            $logList = Db::name($this->tableName)
                ->field('key_3,key_int1')
                ->where('fid', $this->log_fid)
                ->where('key_1', $loginSign)
                ->where('key_int2', $userList['key_int1'])
                ->where('key_int3', $trainType)
                ->order('id asc')
                ->select()->toArray();
            foreach ($logList as $val) {
                $recordList[] = array(
                    'questionId' => $val['key_int1'],
                    'answer' => $val['key_3']
                );
            }
        }

        $questions = $this->getQuestion($trainType);
        $detail = array(
            'sign' => $loginSign,
            'trainType' => $trainType,
            'turns' => $userList['key_int1'],
            'subtitle' => $this->trainList[$trainType]
        );

        View::assign('detail', $detail);
        View::assign('nextQuestion', $nextQuestion);
        View::assign('questions', json_encode($questions, 256));
        View::assign('records', json_encode($recordList,256));
        return view();
    }

    public function datisub()
    {
        $result = array(
            'status' => 0
        );
        $turns = intval($_POST['turns']);
        $trainType = intval($_POST['trainType']);
        $sign = getgpc_new($_POST['sign']);

        $option = getgpc_new($_POST['pick']);
        $id = intval($_POST['id']);
        $right = $_POST['right'];
        $is_true = $right == 'true' ? 1 : 0;

        $signData = explode('#', $sign);
        $uid = intval($signData[0]);
        if($signData[1] != $this->wechat_user['openid'] || !$uid){
            $result['msg'] = '授权异常';
            echo json_encode($result);
            exit;
        }
        $exist = Db::name($this->tableName)
            ->where('fid', $this->log_fid)
            ->where('key_1', $sign)
            ->where('key_int2', $turns)
            ->where('key_int3', $trainType)
            ->where('key_int1', $id)
            ->count();

        if(!$exist){
            $status = 1;
            $last = Db::name($this->tableName)
                ->field('id')
                ->where('fid', $this->fid)
                ->where('key_int1', $trainType)
                ->where('key_int3', 1)
                ->order('id desc')
                ->find();
            if($last['id'] == $id){
                $status = 2;
                $thisTrueTotal = Db::name($this->tableName)
                    ->where('fid', $this->log_fid)
                    ->where('key_1', $sign)
                    ->where('key_int2', $turns)
                    ->where('key_int3', $trainType)
                    ->where('key_2', 1)
                    ->count();
                $result['correctNum'] = $thisTrueTotal;
            }
            $insertData = array(
                'openid' => $this->wechat_user['openid'],
                'key_1'  => $sign,
                'key_2'  => $is_true,
                'key_3'  => $option,
                'key_int1' => $id,
                'key_int2' => $turns,
                'key_int3' => $trainType,
                'fid' => $this->log_fid,
                'addtime' => time()
            );
            $res = Db::name($this->tableName)->save($insertData);
            if($res){
                $update = array(
                    'key_int2' => Db::raw('key_int2+1'),
                    'key_4'    => date('Y-m-d H:i:s')
                );
                Db::name($this->tableName)
                    ->where('fid', $this->user_fid)
                    ->where('id', $uid)
                    ->where('openid', $this->wechat_user['openid'])
                    ->update($update);
                $result['status'] = $status;
                $result['msg'] = '保存成功';
            }else{
                $result['status'] = 0;
                $result['msg'] = '保存失败';
            }
        }else{
            //已答题的也通过
            $result['status'] = 1;
            $result['msg'] = '重复答题';
        }
        echo json_encode($result);
        die;
    }

    public function errors()
    {
        $trainType = intval($_GET['class']);
        if(!in_array($trainType,array(1,2))){
            header("location:index");
            exit;
        }
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_7,key_int1,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
        if(!$userList){
            header('location:index');
            exit;
        }
        if($userList['key_7'] && strtotime($userList['key_7']) < time()){
            echo '<script>alert("抱歉，您已超出本次训练时间！");window.history.go(-1);</script>';
            exit;
        }

        //读取错题
        $loginSign = $userList['id'].'#'.$userList['openid'];
        $wrongList = Db::name($this->tableName)
            ->field('key_3,key_int1')
            ->where('fid', $this->log_fid)
            ->where('key_int3', $trainType)
            ->where('key_1', $loginSign)
            ->where('key_int2', $userList['key_int1'])
            ->where('key_2', 0)
            ->order('id asc')
            ->select()->toArray();
        if(!$wrongList){
            echo '<script>alert("本次训练未检测到错误题");window.history.go(-1);</script>';
            exit;
        }

        $wData = array();
        foreach ($wrongList as $v){
            $wData[$v['key_int1']] = $v['key_3'];
        }
        $questions = $this->getQuestion($trainType);
        $lists = $recordList = array();
        foreach ($questions as $v)
        {
            if(array_key_exists($v['id'], $wData)){
                // 保持与 get_questions 方法一致的 records 结构
                $recordList[] = array(
                    'questionId' => $v['id'],
                    'answer' => $wData[$v['id']]
                );
                $lists[] = $v;
            }
        }

        View::assign('subtitle', $this->trainList[$trainType]);
        View::assign('lists', json_encode($lists, 256));
        View::assign('records', json_encode($recordList));
        return view();
    }

    protected function trainStatus($trainType)
    {
        $trainList = $this->trainList;
        $progressArray = array(
            0 => '开始训练', 1=> '继续训练', 2=> '重新开始'
        );
        //查询人员名单
        $userList = Db::name($this->tableName)
            ->field('id,openid,key_1,key_int1,key_int2,key_int3')
            ->where('fid', $this->user_fid)
            ->where('key_int3', $trainType)
            ->where('openid', $this->wechat_user['openid'])
            ->where('key_6', 1)
            ->order('id desc')
            ->find();
        $rtData = array(
            'trainTitle' => $trainList[$trainType],
            'trainType'  => $trainType,
        );
        $progressCode = $login_auth = 0;
        $thisTotal = $thisTrueTotal = 0;
        $allowAgain = 0;
        if($userList){
            $login_auth = 1;
            //查询训练情况
            $loginSign = $userList['id'].'#'.$userList['openid'];
            $thisTotal = Db::name($this->tableName)
                ->where('fid', $this->log_fid)
                ->where('key_1', $loginSign)
                ->where('key_int2', $userList['key_int1'])
                ->where('key_int3', $trainType)
                ->count();
            if($thisTotal){
                $thisTrueTotal = Db::name($this->tableName)
                    ->where('fid', $this->log_fid)
                    ->where('key_1', $loginSign)
                    ->where('key_int2', $userList['key_int1'])
                    ->where('key_int3', $trainType)
                    ->where('key_2', 1)
                    ->count();
            }
            $quesTotal = Db::name($this->tableName)
                ->where('fid', $this->fid)
                ->where('key_int1', $trainType)
                ->where('key_int3', 1)
                ->count();
            if ($thisTotal && $thisTotal < $quesTotal) {
                //本轮训练中
                $progressCode = 1;
            } elseif ($thisTotal == $quesTotal) {
                //本轮训练结束
                $progressCode = 2;
            }

            //训练中是否允许重来
            if($userList['key_int2']>0 && $progressCode == 1){
                $allowAgain = 1;
            }
        }
        $rtData['progressTitle'] = $progressArray[$progressCode];
        $rtData['progressCode'] = $progressCode;
        $rtData['login_auth'] = $login_auth;

        //训练情况
        $rtData['thisTotal'] = $thisTotal;
        $rtData['thisTrueTotal'] = $thisTrueTotal;

        $rtData['allowAgain'] = $allowAgain;
        return $rtData;
    }

    protected function getQuestion($trainType)
    {
        $questionsKey = $this->session_id . '_question_'.$trainType;
        $listsJson =  Cache::get($questionsKey);
        if ($this->wechat_user['openid'] == 'oa2Bo1HyHiTuOkgf8rlZryzdXn74') {
            $listsJson = "";
        }
        if (empty($listsJson)) {
            $lists = Db::name($this->tableName)
                ->field('id,key_1,key_2,key_3,key_4,key_5,key_6,key_7,key_8,key_9,key_int2')
                ->where('fid', $this->fid)
                ->where('key_int1', $trainType)
                ->where('key_int3', 1)
                ->order('id asc')
                ->select()->toArray();
            $listsJson = json_encode($lists, 256);
            Cache::set($questionsKey, $listsJson, 600);
        } else {
            $lists = json_decode($listsJson, true);
        }

        $rtData = array();
        foreach ($lists as $v)
        {
            $temp = array();
            $v = $this->autoReplace($v);
            $temp['id'] = $v['id'];
            $temp['tit'] = $v['key_1'];
            $temp['qes'][] = $v['key_2'];
            $temp['qes'][] = $v['key_3'];
            if ($v['key_4']) {
                $temp['qes'][] = $v['key_4'];
            }
            if ($v['key_5']) {
                $temp['qes'][] = $v['key_5'];
            }
            if ($v['key_7']) {
                $temp['qes'][] = $v['key_7'];
            }
            if ($v['key_8']) {
                $temp['qes'][] = $v['key_8'];
            }
            //题型
            $temp['asw'] = $v['key_6'];
            if ($v['key_int2'] == 1) {
                //单选题
                $temp['type'] = 1;
            } elseif ($v['key_int2'] == 3) {
                //多选题
                $temp['type'] = 2;
            } elseif ($v['key_int2'] == 2) {
                //判断题
                $temp['type'] = 3;
            } else {
                //填空题
                $temp['type'] = 4;
                $temp['asw'] = $v['key_9'];
            }
            $rtData[] = $temp;
        }
        return $rtData;
    }

    protected function autoReplace($data)
    {
        //清除空格以及特殊字符
        foreach ($data as $k=>$v){
            $temp = str_replace(' ','',$v);
            $temp = str_replace('　','',$temp);
            if($k == 'key_6'){
                $temp = str_replace('，','',$temp);
                $temp = str_replace(',','',$temp);
//                $temp = str_replace(array(1,2,3,4,5),array('A','B','C','D','E'),$temp);

                $pattern = '/[1-5]{1}/';
                preg_match_all($pattern, $temp, $matches);
                if(isset($matches[0]) && $matches[0]){
                    $temp = implode('',$matches[0]);
                    $temp = str_replace(array(1,2,3,4,5),array('A','B','C','D','E'),$temp);
                }
            }else{
                $temp = str_replace("'",'“',$temp);
                $temp = str_replace('"','“',$temp);
                $temp = str_replace(',','，',$temp);
            }
            $data[$k] = $temp;
        }
        return $data;
    }
    
}